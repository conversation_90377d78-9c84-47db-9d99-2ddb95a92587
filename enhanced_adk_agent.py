import asyncio
import json
from typing import Optional, List, Any, Dict
from litellm import completion
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from google.adk.sessions import InMemorySessionService
from google.adk.runners import Runner
from google.genai import types
from pydantic import BaseModel, ValidationError, Field
from datetime import datetime

# 1. 设置 API Key
import os
os.environ["GEMINI_API_KEY"] = "AIzaSyCLNqIk7Y9_tMpSu_XfLp5s1egJ-WLdzOI"

# 2. 定义Pydantic模型来规范输出格式
class PersonInfo(BaseModel):
    """个人信息模型"""
    name: str = Field(description="姓名")
    age: Optional[int] = Field(None, description="年龄", ge=0, le=150)
    occupation: Optional[str] = Field(None, description="职业")
    location: Optional[str] = Field(None, description="地点")

class TechnicalComparison(BaseModel):
    """技术比较模型"""
    technology1: str = Field(description="第一个技术")
    technology2: str = Field(description="第二个技术")
    differences: List[str] = Field(description="主要区别列表")
    advantages: Dict[str, List[str]] = Field(description="各自优势")
    use_cases: Dict[str, List[str]] = Field(description="使用场景")

class LearningAdvice(BaseModel):
    """学习建议模型"""
    topic: str = Field(description="学习主题")
    beginner_tips: List[str] = Field(description="初学者建议")
    intermediate_tips: List[str] = Field(description="进阶建议")
    resources: List[str] = Field(description="推荐资源")
    timeline: Optional[str] = Field(None, description="学习时间线")

class GeneralResponse(BaseModel):
    """通用响应模型"""
    content: str = Field(description="回答内容")
    category: str = Field(description="回答类别")
    confidence: Optional[float] = Field(None, description="置信度", ge=0.0, le=1.0)
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")

# 3. 创建增强的Agent类
class EnhancedADKAgent:
    def __init__(self):
        # 创建 LLM 模型实例
        self.model = LiteLlm(model="gemini/gemini-2.5-flash")
        
        # 创建主要的对话Agent
        self.main_agent = Agent(
            name="enhanced_assistant",
            model=self.model,
            instruction="""
            你是一个专业的AI助手。请根据用户的问题类型，提供结构化的回答：
            
            1. 如果是个人介绍相关，请提供姓名、年龄、职业、地点等信息
            2. 如果是技术比较，请详细对比两个技术的区别、优势和使用场景
            3. 如果是学习建议，请提供分层次的建议和资源推荐
            4. 其他问题请提供通用格式的回答
            
            请用中文回答，并尽量提供结构化的信息。
            """,
            description="增强的中文对话助手，支持结构化输出"
        )
        
        # 创建修复Agent
        self.fix_agent = Agent(
            name="fix_assistant", 
            model=self.model,
            instruction="""
            你是一个专门负责修复和完善回答的AI助手。
            当收到一个不完整或格式不正确的回答时，请：
            1. 分析缺失的信息
            2. 补充必要的细节
            3. 确保回答符合要求的结构化格式
            4. 保持回答的准确性和完整性
            
            请用中文回答。
            """,
            description="回答修复助手"
        )
        
        # 初始化会话服务和运行器
        self.session_service = InMemorySessionService()
        self.main_runner = Runner(
            app_name="enhanced_chat_app",
            agent=self.main_agent,
            session_service=self.session_service
        )
        self.fix_runner = Runner(
            app_name="fix_chat_app", 
            agent=self.fix_agent,
            session_service=self.session_service
        )
        
        # 定义问题类型和对应的Pydantic模型
        self.response_models = {
            "personal_info": PersonInfo,
            "tech_comparison": TechnicalComparison,
            "learning_advice": LearningAdvice,
            "general": GeneralResponse
        }

    def classify_question(self, question: str) -> str:
        """分类用户问题"""
        question_lower = question.lower()

        # 个人信息关键词检测
        personal_keywords = ["介绍", "我是", "我叫", "姓名", "年龄", "职业", "岁", "工作", "住在"]
        if any(keyword in question_lower for keyword in personal_keywords):
            return "personal_info"

        # 技术比较关键词检测
        tech_keywords = ["区别", "比较", "差异", "vs", "对比", "不同", "哪个好", "优缺点"]
        if any(keyword in question_lower for keyword in tech_keywords):
            return "tech_comparison"

        # 学习建议关键词检测
        learning_keywords = ["学习", "建议", "推荐", "如何学", "怎么学", "教程", "入门", "指导"]
        if any(keyword in question_lower for keyword in learning_keywords):
            return "learning_advice"

        return "general"

    async def get_agent_response(self, user_input: str, runner: Runner, user_id: str = "user_001", session_id: str = "session_001") -> str:
        """获取Agent响应"""
        user_message = types.Content(
            role='user',
            parts=[types.Part(text=user_input)]
        )
        
        final_response = ""
        async for event in runner.run_async(
            user_id=user_id,
            session_id=session_id,
            new_message=user_message
        ):
            if event.is_final_response() and event.content and event.content.parts:
                final_response = event.content.parts[0].text
                break
        
        return final_response

    async def extract_structured_data(self, response: str, model_class: BaseModel) -> Optional[BaseModel]:
        """尝试从响应中提取结构化数据"""
        try:
            # 尝试直接解析JSON
            if response.strip().startswith('{'):
                data = json.loads(response)
                return model_class.model_validate(data)

            # 如果不是JSON，使用LLM提取结构化信息
            extraction_prompt = f"""
            请从以下文本中提取信息，并严格按照JSON格式返回，符合以下结构：

            JSON Schema:
            {json.dumps(model_class.model_json_schema(), ensure_ascii=False, indent=2)}

            文本内容：
            {response}

            要求：
            1. 只返回有效的JSON格式
            2. 确保所有必需字段都有值
            3. 不要添加任何其他文字说明
            4. 如果某些信息在文本中没有，请合理推断或使用null

            JSON:
            """

            # 使用extraction agent来提取结构化数据
            # 使用相同的session_id避免创建新会话
            extraction_response = await self.get_agent_response(
                extraction_prompt,
                self.fix_runner,
                user_id="user_001",
                session_id="session_001"
            )

            # 清理响应，只保留JSON部分
            json_start = extraction_response.find('{')
            json_end = extraction_response.rfind('}') + 1

            if json_start != -1 and json_end > json_start:
                json_str = extraction_response[json_start:json_end]
                data = json.loads(json_str)
                return model_class.model_validate(data)

            return None

        except (json.JSONDecodeError, ValidationError) as e:
            print(f"数据提取失败: {e}")
            return None

    async def chat_with_validation(self, user_input: str, max_retries: int = 2) -> Dict[str, Any]:
        """带验证的对话处理"""
        question_type = self.classify_question(user_input)
        target_model = self.response_models[question_type]
        
        print(f"问题类型: {question_type}")
        print(f"目标模型: {target_model.__name__}")
        
        # 创建会话
        await self.session_service.create_session(
            app_name="enhanced_chat_app",
            user_id="user_001",
            session_id="session_001"
        )

        # 为fix_runner也创建会话
        await self.session_service.create_session(
            app_name="fix_chat_app",
            user_id="user_001",
            session_id="session_001"
        )
        
        for attempt in range(max_retries + 1):
            print(f"\n--- 尝试 {attempt + 1} ---")
            
            # 获取主Agent的响应
            if attempt == 0:
                response = await self.get_agent_response(user_input, self.main_runner)
            else:
                # 使用修复Agent
                fix_prompt = f"""
                原始问题: {user_input}
                
                之前的回答: {response}
                
                这个回答需要符合以下结构化格式：
                {target_model.model_json_schema()}
                
                请修复并完善这个回答，确保包含所有必要的信息。
                """
                response = await self.get_agent_response(fix_prompt, self.fix_runner)
            
            print(f"Agent响应: {response}")
            
            # 尝试提取结构化数据
            structured_data = await self.extract_structured_data(response, target_model)
            
            if structured_data:
                print("✅ 验证成功!")
                return {
                    "success": True,
                    "response": response,
                    "structured_data": structured_data.model_dump(),
                    "attempts": attempt + 1,
                    "question_type": question_type
                }
            else:
                print(f"❌ 验证失败，尝试修复... (尝试 {attempt + 1}/{max_retries + 1})")
        
        # 如果所有尝试都失败，返回原始响应
        return {
            "success": False,
            "response": response,
            "structured_data": None,
            "attempts": max_retries + 1,
            "question_type": question_type,
            "error": "无法提取结构化数据"
        }

# 4. 示例使用
async def main():
    agent = EnhancedADKAgent()
    
    # 测试不同类型的问题
    test_questions = [
        "你好！请自我介绍一下",
        "Python 和 JavaScript 有什么主要区别？", 
        "能给我推荐一些学习编程的建议吗？",
        "今天天气怎么样？"
    ]
    
    for question in test_questions:
        print(f"\n{'='*60}")
        print(f"👤 用户: {question}")
        
        result = await agent.chat_with_validation(question)
        
        print(f"🤖 助手: {result['response']}")
        print(f"📊 结果: {'成功' if result['success'] else '失败'}")
        print(f"🔄 尝试次数: {result['attempts']}")
        print(f"📝 问题类型: {result['question_type']}")
        
        if result['structured_data']:
            print(f"📋 结构化数据: {json.dumps(result['structured_data'], ensure_ascii=False, indent=2)}")

# 运行示例
if __name__ == "__main__":
    asyncio.run(main())
