{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 增强ADK Agent - 快速测试版\n", "\n", "这是一个简化版本的Notebook，专门用于快速测试增强的ADK Agent功能。\n", "\n", "## 🚀 快速开始\n", "1. 运行所有单元格\n", "2. 在最后的测试区域修改问题\n", "3. 查看结构化输出结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入所有必要的模块\n", "import sys\n", "import os\n", "\n", "# 添加当前目录到Python路径\n", "sys.path.append('.')\n", "\n", "# 导入我们的增强Agent\n", "from enhanced_adk_agent import EnhancedADKAgent\n", "import asyncio\n", "import json\n", "\n", "print(\"✅ 模块导入成功！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建Agent实例\n", "agent = EnhancedADKAgent()\n", "print(\"🤖 Agent创建成功！\")\n", "print(f\"支持的问题类型: {list(agent.response_models.keys())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试问题分类功能\n", "test_cases = [\n", "    \"我叫张三，25岁，软件工程师\",\n", "    \"Python和Java有什么区别\",\n", "    \"如何学习机器学习\",\n", "    \"今天天气怎么样\"\n", "]\n", "\n", "print(\"🏷️ 问题分类测试:\")\n", "for question in test_cases:\n", "    category = agent.classify_question(question)\n", "    print(f\"  '{question}' -> {category}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义测试函数\n", "async def test_agent(question: str):\n", "    \"\"\"测试Agent并显示结果\"\"\"\n", "    print(f\"\\n🔍 测试问题: {question}\")\n", "    print(\"=\" * 60)\n", "    \n", "    try:\n", "        # 调用Agent\n", "        result = await agent.chat_with_validation(question)\n", "        \n", "        # 显示结果\n", "        print(f\"\\n📝 AI回答:\")\n", "        print(result['response'])\n", "        \n", "        print(f\"\\n📊 验证结果:\")\n", "        print(f\"  成功: {'✅' if result['success'] else '❌'}\")\n", "        print(f\"  尝试次数: {result['attempts']}\")\n", "        print(f\"  问题类型: {result['question_type']}\")\n", "        \n", "        if result['success'] and result['structured_data']:\n", "            print(f\"\\n📋 结构化数据:\")\n", "            print(json.dumps(result['structured_data'], ensure_ascii=False, indent=2))\n", "        \n", "        return result\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 测试失败: {str(e)}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        return None\n", "\n", "print(\"🔧 测试函数定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 测试区域\n", "\n", "在下面的单元格中修改问题进行测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试个人信息提取\n", "personal_question = \"你好，我叫李明，今年30岁，是一名数据科学家，在上海工作\"\n", "personal_result = await test_agent(personal_question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试技术比较\n", "tech_question = \"React和Vue.js有什么主要区别？请详细比较一下\"\n", "tech_result = await test_agent(tech_question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试学习建议\n", "learning_question = \"我想学习人工智能，能给我一些系统的学习建议吗？\"\n", "learning_result = await test_agent(learning_question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 自定义测试 - 在这里修改你想测试的问题\n", "custom_question = \"Python和JavaScript在数据处理方面有什么不同？\"\n", "\n", "# 运行测试\n", "custom_result = await test_agent(custom_question)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 批量测试\n", "\n", "测试多个问题并比较结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 批量测试多个问题\n", "batch_questions = [\n", "    \"我是王小红，26岁，在北京做UI设计师\",\n", "    \"MySQL和PostgreSQL哪个更适合初学者？\",\n", "    \"如何快速入门前端开发？\",\n", "    \"什么是区块链技术？\"\n", "]\n", "\n", "print(\"🔄 批量测试开始...\")\n", "batch_results = []\n", "\n", "for i, question in enumerate(batch_questions, 1):\n", "    print(f\"\\n{'='*80}\")\n", "    print(f\"测试 {i}/{len(batch_questions)}\")\n", "    result = await test_agent(question)\n", "    batch_results.append(result)\n", "\n", "print(f\"\\n🎉 批量测试完成！\")\n", "print(f\"成功率: {sum(1 for r in batch_results if r and r['success'])}/{len(batch_results)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 结果分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析测试结果\n", "def analyze_results(results):\n", "    \"\"\"分析测试结果\"\"\"\n", "    if not results:\n", "        print(\"❌ 没有测试结果可分析\")\n", "        return\n", "    \n", "    valid_results = [r for r in results if r is not None]\n", "    \n", "    print(\"📊 测试结果分析:\")\n", "    print(f\"  总测试数: {len(results)}\")\n", "    print(f\"  有效结果: {len(valid_results)}\")\n", "    \n", "    if valid_results:\n", "        success_count = sum(1 for r in valid_results if r['success'])\n", "        print(f\"  验证成功: {success_count}/{len(valid_results)}\")\n", "        print(f\"  成功率: {success_count/len(valid_results)*100:.1f}%\")\n", "        \n", "        # 按问题类型统计\n", "        type_stats = {}\n", "        for result in valid_results:\n", "            qtype = result['question_type']\n", "            if qtype not in type_stats:\n", "                type_stats[qtype] = {'total': 0, 'success': 0}\n", "            type_stats[qtype]['total'] += 1\n", "            if result['success']:\n", "                type_stats[qtype]['success'] += 1\n", "        \n", "        print(f\"\\n  按类型统计:\")\n", "        for qtype, stats in type_stats.items():\n", "            rate = stats['success']/stats['total']*100 if stats['total'] > 0 else 0\n", "            print(f\"    {qtype}: {stats['success']}/{stats['total']} ({rate:.1f}%)\")\n", "        \n", "        # 平均尝试次数\n", "        avg_attempts = sum(r['attempts'] for r in valid_results) / len(valid_results)\n", "        print(f\"\\n  平均尝试次数: {avg_attempts:.1f}\")\n", "\n", "# 分析批量测试结果\n", "analyze_results(batch_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💡 使用提示\n", "\n", "1. **修改问题**: 在测试单元格中修改问题变量，然后重新运行\n", "2. **查看结果**: 注意观察验证成功率和结构化数据格式\n", "3. **问题类型**: 系统会自动识别问题类型并选择对应的数据模型\n", "4. **重试机制**: 如果第一次验证失败，系统会自动重试最多2次\n", "\n", "## 🔧 故障排除\n", "\n", "- **导入错误**: 确保 `enhanced_adk_agent.py` 文件在当前目录\n", "- **API错误**: 检查Gemini API Key是否正确设置\n", "- **验证失败**: 可能是模型输出格式不规范，系统会自动重试\n", "\n", "## 📚 扩展功能\n", "\n", "- 添加新的Pydantic模型\n", "- 优化问题分类逻辑\n", "- 增加更多测试用例\n", "- 集成其他LLM模型"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}