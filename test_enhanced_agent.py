#!/usr/bin/env python3
"""
测试增强的ADK Agent
这个脚本用于测试带有Pydantic验证的ADK Agent功能
"""

import asyncio
import json
from enhanced_adk_agent import EnhancedADKAgent

async def test_single_question(agent: EnhancedADKAgent, question: str):
    """测试单个问题"""
    print(f"\n{'='*80}")
    print(f"🔍 测试问题: {question}")
    print(f"{'='*80}")
    
    try:
        result = await agent.chat_with_validation(question)
        
        print(f"\n📝 原始回答:")
        print(f"   {result['response']}")
        
        print(f"\n📊 验证结果:")
        print(f"   成功: {'✅' if result['success'] else '❌'}")
        print(f"   尝试次数: {result['attempts']}")
        print(f"   问题类型: {result['question_type']}")
        
        if result['success'] and result['structured_data']:
            print(f"\n📋 结构化数据:")
            print(json.dumps(result['structured_data'], ensure_ascii=False, indent=4))
        elif not result['success']:
            print(f"\n❌ 错误信息: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"\n💥 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_personal_info():
    """测试个人信息提取"""
    agent = EnhancedADKAgent()
    
    questions = [
        "你好，我叫张三，今年25岁，是一名软件工程师，住在北京",
        "请介绍一下你自己",
        "我是李四，在上海工作，做产品经理的"
    ]
    
    print("🧑 测试个人信息提取功能")
    for question in questions:
        await test_single_question(agent, question)

async def test_tech_comparison():
    """测试技术比较功能"""
    agent = EnhancedADKAgent()
    
    questions = [
        "Python和Java有什么区别？",
        "React和Vue.js哪个更好？",
        "MySQL和PostgreSQL的主要差异是什么？"
    ]
    
    print("⚡ 测试技术比较功能")
    for question in questions:
        await test_single_question(agent, question)

async def test_learning_advice():
    """测试学习建议功能"""
    agent = EnhancedADKAgent()
    
    questions = [
        "我想学习机器学习，有什么建议吗？",
        "如何学好前端开发？",
        "给我一些学习数据科学的建议"
    ]
    
    print("📚 测试学习建议功能")
    for question in questions:
        await test_single_question(agent, question)

async def test_general_questions():
    """测试通用问题功能"""
    agent = EnhancedADKAgent()
    
    questions = [
        "今天天气怎么样？",
        "什么是人工智能？",
        "推荐一些好看的电影"
    ]
    
    print("💬 测试通用问题功能")
    for question in questions:
        await test_single_question(agent, question)

async def test_validation_robustness():
    """测试验证机制的鲁棒性"""
    agent = EnhancedADKAgent()
    
    # 测试可能导致验证失败的问题
    challenging_questions = [
        "我叫...",  # 不完整的个人信息
        "比较一下",  # 不明确的比较请求
        "学习建议",  # 过于简单的学习请求
    ]
    
    print("🔧 测试验证机制鲁棒性")
    for question in challenging_questions:
        await test_single_question(agent, question)

async def run_comprehensive_test():
    """运行全面测试"""
    print("🚀 开始全面测试增强的ADK Agent")
    print("=" * 100)
    
    try:
        # 测试各种功能
        await test_personal_info()
        await test_tech_comparison()
        await test_learning_advice()
        await test_general_questions()
        await test_validation_robustness()
        
        print("\n" + "=" * 100)
        print("✅ 所有测试完成!")
        
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

async def interactive_test():
    """交互式测试"""
    agent = EnhancedADKAgent()
    
    print("🎯 交互式测试模式")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 50)
    
    while True:
        try:
            question = input("\n👤 请输入问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break
                
            if not question:
                print("❌ 请输入有效问题")
                continue
                
            await test_single_question(agent, question)
            
        except KeyboardInterrupt:
            print("\n👋 用户中断，再见!")
            break
        except Exception as e:
            print(f"❌ 处理问题时出错: {str(e)}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        # 交互式模式
        asyncio.run(interactive_test())
    else:
        # 自动测试模式
        asyncio.run(run_comprehensive_test())

if __name__ == "__main__":
    main()
