{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 增强的Google ADK Agent with Pydantic验证\n", "\n", "这个Notebook展示了如何使用Google ADK构建一个带有Pydantic验证和自动修复功能的AI代理。\n", "\n", "## 主要特性\n", "- 🎯 智能问题分类\n", "- 📊 结构化数据提取\n", "- 🔧 自动验证和修复\n", "- 📋 多种数据模型支持"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 安装和导入依赖"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的包（如果还没有安装）\n", "# !pip install google-adk litellm pydantic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import json\n", "import os\n", "from typing import Optional, List, Any, Dict\n", "from datetime import datetime\n", "\n", "# Google ADK imports\n", "from litellm import completion\n", "from google.adk.agents import Agent\n", "from google.adk.models.lite_llm import LiteLlm\n", "from google.adk.sessions import InMemorySessionService\n", "from google.adk.runners import Runner\n", "from google.genai import types\n", "\n", "# Pydantic imports\n", "from pydantic import BaseModel, ValidationError, Field\n", "\n", "print(\"✅ 所有依赖导入成功！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 设置API Key"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置你的Gemini API Key\n", "os.environ[\"GEMINI_API_KEY\"] = \"AIzaSyCLNqIk7Y9_tMpSu_XfLp5s1egJ-WLdzOI\"\n", "\n", "print(\"🔑 API Key 设置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 定义Pydantic数据模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PersonInfo(BaseModel):\n", "    \"\"\"个人信息模型\"\"\"\n", "    name: str = Field(description=\"姓名\")\n", "    age: Optional[int] = Field(None, description=\"年龄\", ge=0, le=150)\n", "    occupation: Optional[str] = Field(None, description=\"职业\")\n", "    location: Optional[str] = Field(None, description=\"地点\")\n", "\n", "class TechnicalComparison(BaseModel):\n", "    \"\"\"技术比较模型\"\"\"\n", "    technology1: str = Field(description=\"第一个技术\")\n", "    technology2: str = Field(description=\"第二个技术\")\n", "    differences: List[str] = Field(description=\"主要区别列表\")\n", "    advantages: Dict[str, List[str]] = Field(description=\"各自优势\")\n", "    use_cases: Dict[str, List[str]] = Field(description=\"使用场景\")\n", "\n", "class LearningAdvice(BaseModel):\n", "    \"\"\"学习建议模型\"\"\"\n", "    topic: str = Field(description=\"学习主题\")\n", "    beginner_tips: List[str] = Field(description=\"初学者建议\")\n", "    intermediate_tips: List[str] = Field(description=\"进阶建议\")\n", "    resources: List[str] = Field(description=\"推荐资源\")\n", "    timeline: Optional[str] = Field(None, description=\"学习时间线\")\n", "\n", "class GeneralResponse(BaseModel):\n", "    \"\"\"通用响应模型\"\"\"\n", "    content: str = Field(description=\"回答内容\")\n", "    category: str = Field(description=\"回答类别\")\n", "    confidence: Optional[float] = Field(None, description=\"置信度\", ge=0.0, le=1.0)\n", "    timestamp: datetime = Field(default_factory=datetime.now, description=\"时间戳\")\n", "\n", "print(\"📋 Pydantic模型定义完成\")\n", "print(f\"PersonInfo字段: {list(PersonInfo.model_fields.keys())}\")\n", "print(f\"TechnicalComparison字段: {list(TechnicalComparison.model_fields.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 创建增强的ADK Agent类"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class EnhancedADKAgent:\n", "    def __init__(self):\n", "        # 创建 LLM 模型实例\n", "        self.model = LiteLlm(model=\"gemini/gemini-2.5-flash\")\n", "        \n", "        # 创建主要的对话Agent\n", "        self.main_agent = Agent(\n", "            name=\"enhanced_assistant\",\n", "            model=self.model,\n", "            instruction=\"\"\"\n", "            你是一个专业的AI助手。请根据用户的问题类型，提供结构化的回答：\n", "            \n", "            1. 如果是个人介绍相关，请提供姓名、年龄、职业、地点等信息\n", "            2. 如果是技术比较，请详细对比两个技术的区别、优势和使用场景\n", "            3. 如果是学习建议，请提供分层次的建议和资源推荐\n", "            4. 其他问题请提供通用格式的回答\n", "            \n", "            请用中文回答，并尽量提供结构化的信息。\n", "            \"\"\",\n", "            description=\"增强的中文对话助手，支持结构化输出\"\n", "        )\n", "        \n", "        # 创建修复Agent\n", "        self.fix_agent = Agent(\n", "            name=\"fix_assistant\", \n", "            model=self.model,\n", "            instruction=\"\"\"\n", "            你是一个专门负责修复和完善回答的AI助手。\n", "            当收到一个不完整或格式不正确的回答时，请：\n", "            1. 分析缺失的信息\n", "            2. 补充必要的细节\n", "            3. 确保回答符合要求的结构化格式\n", "            4. 保持回答的准确性和完整性\n", "            \n", "            请用中文回答。\n", "            \"\"\",\n", "            description=\"回答修复助手\"\n", "        )\n", "        \n", "        # 初始化会话服务和运行器\n", "        self.session_service = InMemorySessionService()\n", "        self.main_runner = Runner(\n", "            app_name=\"enhanced_chat_app\",\n", "            agent=self.main_agent,\n", "            session_service=self.session_service\n", "        )\n", "        self.fix_runner = Runner(\n", "            app_name=\"fix_chat_app\", \n", "            agent=self.fix_agent,\n", "            session_service=self.session_service\n", "        )\n", "        \n", "        # 定义问题类型和对应的Pydantic模型\n", "        self.response_models = {\n", "            \"personal_info\": PersonInfo,\n", "            \"tech_comparison\": TechnicalComparison,\n", "            \"learning_advice\": LearningAdvice,\n", "            \"general\": GeneralResponse\n", "        }\n", "\n", "    def classify_question(self, question: str) -> str:\n", "        \"\"\"分类用户问题\"\"\"\n", "        question_lower = question.lower()\n", "        \n", "        # 个人信息关键词检测\n", "        personal_keywords = [\"介绍\", \"我是\", \"我叫\", \"姓名\", \"年龄\", \"职业\", \"岁\", \"工作\", \"住在\"]\n", "        if any(keyword in question_lower for keyword in personal_keywords):\n", "            return \"personal_info\"\n", "        \n", "        # 技术比较关键词检测\n", "        tech_keywords = [\"区别\", \"比较\", \"差异\", \"vs\", \"对比\", \"不同\", \"哪个好\", \"优缺点\"]\n", "        if any(keyword in question_lower for keyword in tech_keywords):\n", "            return \"tech_comparison\"\n", "        \n", "        # 学习建议关键词检测\n", "        learning_keywords = [\"学习\", \"建议\", \"推荐\", \"如何学\", \"怎么学\", \"教程\", \"入门\", \"指导\"]\n", "        if any(keyword in question_lower for keyword in learning_keywords):\n", "            return \"learning_advice\"\n", "        \n", "        return \"general\"\n", "\n", "print(\"🤖 EnhancedADKAgent类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 添加核心方法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 为EnhancedADKAgent类添加核心方法\n", "\n", "async def get_agent_response(self, user_input: str, runner: <PERSON>, user_id: str = \"user_001\", session_id: str = \"session_001\") -> str:\n", "    \"\"\"获取Agent响应\"\"\"\n", "    user_message = types.Content(\n", "        role='user',\n", "        parts=[types.Part(text=user_input)]\n", "    )\n", "    \n", "    final_response = \"\"\n", "    async for event in runner.run_async(\n", "        user_id=user_id,\n", "        session_id=session_id,\n", "        new_message=user_message\n", "    ):\n", "        if event.is_final_response() and event.content and event.content.parts:\n", "            final_response = event.content.parts[0].text\n", "            break\n", "    \n", "    return final_response\n", "\n", "async def extract_structured_data(self, response: str, model_class: BaseModel) -> Optional[BaseModel]:\n", "    \"\"\"尝试从响应中提取结构化数据\"\"\"\n", "    try:\n", "        # 尝试直接解析JSON\n", "        if response.strip().startswith('{'):\n", "            data = json.loads(response)\n", "            return model_class.model_validate(data)\n", "        \n", "        # 如果不是JSON，使用LLM提取结构化信息\n", "        extraction_prompt = f\"\"\"\n", "        请从以下文本中提取信息，并严格按照JSON格式返回，符合以下结构：\n", "        \n", "        JSON Schema:\n", "        {json.dumps(model_class.model_json_schema(), ensure_ascii=False, indent=2)}\n", "        \n", "        文本内容：\n", "        {response}\n", "        \n", "        要求：\n", "        1. 只返回有效的JSON格式\n", "        2. 确保所有必需字段都有值\n", "        3. 不要添加任何其他文字说明\n", "        4. 如果某些信息在文本中没有，请合理推断或使用null\n", "        \n", "        JSON:\n", "        \"\"\"\n", "        \n", "        # 使用extraction agent来提取结构化数据\n", "        extraction_response = await self.get_agent_response(\n", "            extraction_prompt, \n", "            self.fix_runner,\n", "            user_id=\"extractor_001\",\n", "            session_id=\"extract_001\"\n", "        )\n", "        \n", "        # 清理响应，只保留JSON部分\n", "        json_start = extraction_response.find('{')\n", "        json_end = extraction_response.rfind('}') + 1\n", "        \n", "        if json_start != -1 and json_end > json_start:\n", "            json_str = extraction_response[json_start:json_end]\n", "            data = json.loads(json_str)\n", "            return model_class.model_validate(data)\n", "        \n", "        return None\n", "        \n", "    except (json.J<PERSON>rror, ValidationError) as e:\n", "        print(f\"数据提取失败: {e}\")\n", "        return None\n", "\n", "async def chat_with_validation(self, user_input: str, max_retries: int = 2) -> Dict[str, Any]:\n", "    \"\"\"带验证的对话处理\"\"\"\n", "    question_type = self.classify_question(user_input)\n", "    target_model = self.response_models[question_type]\n", "    \n", "    print(f\"问题类型: {question_type}\")\n", "    print(f\"目标模型: {target_model.__name__}\")\n", "    \n", "    # 创建会话\n", "    await self.session_service.create_session(\n", "        app_name=\"enhanced_chat_app\",\n", "        user_id=\"user_001\", \n", "        session_id=\"session_001\"\n", "    )\n", "    \n", "    for attempt in range(max_retries + 1):\n", "        print(f\"\\n--- 尝试 {attempt + 1} ---\")\n", "        \n", "        # 获取主Agent的响应\n", "        if attempt == 0:\n", "            response = await self.get_agent_response(user_input, self.main_runner)\n", "        else:\n", "            # 使用修复Agent\n", "            fix_prompt = f\"\"\"\n", "            原始问题: {user_input}\n", "            \n", "            之前的回答: {response}\n", "            \n", "            这个回答需要符合以下结构化格式：\n", "            {target_model.model_json_schema()}\n", "            \n", "            请修复并完善这个回答，确保包含所有必要的信息。\n", "            \"\"\"\n", "            response = await self.get_agent_response(fix_prompt, self.fix_runner)\n", "        \n", "        print(f\"Agent响应: {response[:200]}...\")\n", "        \n", "        # 尝试提取结构化数据\n", "        structured_data = await self.extract_structured_data(response, target_model)\n", "        \n", "        if structured_data:\n", "            print(\"✅ 验证成功!\")\n", "            return {\n", "                \"success\": True,\n", "                \"response\": response,\n", "                \"structured_data\": structured_data.model_dump(),\n", "                \"attempts\": attempt + 1,\n", "                \"question_type\": question_type\n", "            }\n", "        else:\n", "            print(f\"❌ 验证失败，尝试修复... (尝试 {attempt + 1}/{max_retries + 1})\")\n", "    \n", "    # 如果所有尝试都失败，返回原始响应\n", "    return {\n", "        \"success\": <PERSON><PERSON><PERSON>,\n", "        \"response\": response,\n", "        \"structured_data\": None,\n", "        \"attempts\": max_retries + 1,\n", "        \"question_type\": question_type,\n", "        \"error\": \"无法提取结构化数据\"\n", "    }\n", "\n", "# 将方法绑定到类\n", "EnhancedADKAgent.get_agent_response = get_agent_response\n", "EnhancedADKAgent.extract_structured_data = extract_structured_data\n", "EnhancedADKAgent.chat_with_validation = chat_with_validation\n", "\n", "print(\"✅ 所有方法添加完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 测试问题分类功能"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建Agent实例并测试分类功能\n", "agent = EnhancedADKAgent()\n", "\n", "# 测试问题分类\n", "test_questions = [\n", "    (\"我叫张三，今年25岁，是软件工程师\", \"personal_info\"),\n", "    (\"Python和Java有什么区别？\", \"tech_comparison\"),\n", "    (\"如何学习机器学习？\", \"learning_advice\"),\n", "    (\"今天天气怎么样？\", \"general\")\n", "]\n", "\n", "print(\"🏷️ 问题分类测试:\")\n", "print(\"-\" * 50)\n", "\n", "for question, expected in test_questions:\n", "    result = agent.classify_question(question)\n", "    status = \"✅\" if result == expected else \"❌\"\n", "    print(f\"{status} '{question}' -> {result} (期望: {expected})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 测试Pydantic模型验证"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试PersonInfo模型\n", "print(\"📋 测试PersonInfo模型:\")\n", "try:\n", "    person = PersonInfo(\n", "        name=\"张三\",\n", "        age=25,\n", "        occupation=\"软件工程师\",\n", "        location=\"北京\"\n", "    )\n", "    print(\"✅ PersonInfo验证成功\")\n", "    print(f\"数据: {person.model_dump()}\")\n", "except Exception as e:\n", "    print(f\"❌ PersonInfo验证失败: {e}\")\n", "\n", "print(\"\\n\" + \"-\" * 50)\n", "\n", "# 测试TechnicalComparison模型\n", "print(\"⚡ 测试TechnicalComparison模型:\")\n", "try:\n", "    comparison = TechnicalComparison(\n", "        technology1=\"Python\",\n", "        technology2=\"JavaScript\",\n", "        differences=[\"语法不同\", \"运行环境不同\", \"应用场景不同\"],\n", "        advantages={\n", "            \"Python\": [\"简洁易读\", \"丰富的库\", \"数据科学支持\"],\n", "            \"JavaScript\": [\"前端必备\", \"异步处理\", \"生态丰富\"]\n", "        },\n", "        use_cases={\n", "            \"Python\": [\"数据科学\", \"机器学习\", \"后端开发\"],\n", "            \"JavaScript\": [\"前端开发\", \"Node.js后端\", \"移动应用\"]\n", "        }\n", "    )\n", "    print(\"✅ TechnicalComparison验证成功\")\n", "    print(f\"技术对比: {comparison.technology1} vs {comparison.technology2}\")\n", "    print(f\"区别数量: {len(comparison.differences)}\")\n", "except Exception as e:\n", "    print(f\"❌ TechnicalComparison验证失败: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 实际对话测试（需要API Key）\n", "\n", "⚠️ **注意**: 以下单元格需要有效的Gemini API Key才能运行"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 快速测试特定问题\n", "async def quick_test(question: str):\n", "    print(f\"🔍 快速测试: {question}\")\n", "    print(\"-\" * 50)\n", "    \n", "    try:\n", "        result = await agent.chat_with_validation(question)\n", "        \n", "        print(f\"\\n🤖 回答: {result['response'][:300]}...\")\n", "        print(f\"\\n📊 结果摘要:\")\n", "        print(f\"   验证成功: {'✅' if result['success'] else '❌'}\")\n", "        print(f\"   尝试次数: {result['attempts']}\")\n", "        print(f\"   问题类型: {result['question_type']}\")\n", "        \n", "        if result['success'] and result['structured_data']:\n", "            print(f\"\\n📋 结构化数据:\")\n", "            print(json.dumps(result['structured_data'], ensure_ascii=False, indent=2))\n", "        \n", "        return result\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 测试失败: {str(e)}\")\n", "        return None\n", "\n", "# 测试示例 - 修改这里的问题进行测试\n", "test_question = \"我叫王小明，28岁，在深圳做产品经理\"\n", "result = await quick_test(test_question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试技术比较\n", "tech_question = \"React和Vue.js有什么主要区别？请详细比较一下\"\n", "tech_result = await quick_test(tech_question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试学习建议\n", "learning_question = \"我想学习人工智能，能给我一些系统的学习建议吗？\"\n", "learning_result = await quick_test(learning_question)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 自定义测试区域\n", "\n", "在这里你可以输入自己的问题进行测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 在这里输入你想测试的问题\n", "custom_question = \"输入你的问题\"\n", "\n", "# 如果问题不是默认值，则运行测试\n", "if custom_question != \"输入你的问题\":\n", "    custom_result = await quick_test(custom_question)\n", "else:\n", "    print(\"💡 请在上面的单元格中修改 custom_question 变量，然后重新运行\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. 总结和下一步\n", "\n", "### 🎉 完成的功能\n", "\n", "1. **智能问题分类**: 自动识别个人信息、技术比较、学习建议和通用问题\n", "2. **结构化数据提取**: 使用Pydantic模型确保输出格式一致\n", "3. **自动验证和修复**: 当输出不符合要求时自动重试\n", "4. **多种数据模型**: 支持4种不同类型的结构化输出\n", "\n", "### 🔧 使用建议\n", "\n", "1. **设置API Key**: 确保在第2个单元格中设置了有效的Gemini API Key\n", "2. **逐步测试**: 先运行分类和模型验证测试，再进行实际对话\n", "3. **自定义问题**: 在第9节的自定义测试区域中修改问题进行测试\n", "4. **扩展功能**: 可以添加新的Pydantic模型和问题类型\n", "\n", "### 📈 性能优化建议\n", "\n", "- **缓存机制**: 可以添加响应缓存提高速度\n", "- **批量处理**: 支持同时处理多个问题\n", "- **错误处理**: 更完善的异常处理机制\n", "- **日志记录**: 添加详细的操作日志\n", "\n", "### 🎯 扩展方向\n", "\n", "1. 添加更多数据模型类型\n", "2. 优化问题分类算法\n", "3. 集成更多LLM模型\n", "4. 添加Web界面\n", "5. 部署到生产环境"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}