#!/usr/bin/env python3
"""
测试修复后的增强Agent
"""

import asyncio
import os
import sys
import traceback
import json

# 设置API Key
os.environ["GEMINI_API_KEY"] = "AIzaSyCLNqIk7Y9_tMpSu_XfLp5s1egJ-WLdzOI"

async def test_fixed_enhanced_agent():
    """测试修复后的增强Agent"""
    print("🚀 测试修复后的增强Agent...")
    
    try:
        # 导入修复后的增强Agent
        from enhanced_adk_agent import EnhancedADKAgent
        
        # 创建实例
        agent = EnhancedADKAgent()
        print("✅ 增强Agent创建成功")
        
        # 测试问题分类
        print("\n🏷️ 测试问题分类:")
        test_questions = [
            ("我叫张三，25岁", "personal_info"),
            ("Python和Java的区别", "tech_comparison"), 
            ("如何学习编程", "learning_advice"),
            ("今天天气", "general")
        ]
        
        classification_success = True
        for question, expected in test_questions:
            result = agent.classify_question(question)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{question}' -> {result} (期望: {expected})")
            if result != expected:
                classification_success = False
        
        if classification_success:
            print("✅ 问题分类测试通过")
        else:
            print("⚠️ 问题分类有误差，但不影响核心功能")
        
        # 测试简单对话（个人信息）
        print("\n💬 测试个人信息对话:")
        personal_question = "我叫李明，30岁，是一名软件工程师，在北京工作"
        
        try:
            result = await agent.chat_with_validation(personal_question)
            
            print(f"\n📊 对话结果:")
            print(f"  成功: {'✅' if result['success'] else '❌'}")
            print(f"  问题类型: {result['question_type']}")
            print(f"  尝试次数: {result['attempts']}")
            
            if result['success']:
                print(f"  ✅ 验证成功!")
                if result['structured_data']:
                    print(f"  📋 结构化数据:")
                    print(json.dumps(result['structured_data'], ensure_ascii=False, indent=4))
                return True
            else:
                print(f"  ❌ 验证失败: {result.get('error', '未知错误')}")
                print(f"  原始回答: {result['response'][:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ 对话测试异常: {e}")
            traceback.print_exc()
            return False
            
    except ImportError as e:
        print(f"❌ 导入增强Agent失败: {e}")
        print("请确保enhanced_adk_agent.py文件存在且正确")
        return False
    except Exception as e:
        print(f"❌ 增强Agent测试失败: {e}")
        traceback.print_exc()
        return False

async def test_multiple_questions():
    """测试多个问题类型"""
    print("\n🔄 测试多种问题类型...")
    
    try:
        from enhanced_adk_agent import EnhancedADKAgent
        agent = EnhancedADKAgent()
        
        test_cases = [
            {
                "question": "我是王小红，26岁，UI设计师",
                "expected_type": "personal_info",
                "description": "个人信息提取"
            },
            {
                "question": "React和Vue有什么区别？",
                "expected_type": "tech_comparison", 
                "description": "技术比较"
            },
            {
                "question": "如何学习人工智能？",
                "expected_type": "learning_advice",
                "description": "学习建议"
            }
        ]
        
        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试 {i}: {test_case['description']} ---")
            print(f"问题: {test_case['question']}")
            
            try:
                result = await agent.chat_with_validation(test_case['question'])
                results.append(result)
                
                status = "✅" if result['success'] else "❌"
                print(f"{status} 结果: {'成功' if result['success'] else '失败'}")
                print(f"问题类型: {result['question_type']} (期望: {test_case['expected_type']})")
                
                if result['success'] and result['structured_data']:
                    print("✅ 结构化数据提取成功")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                results.append(None)
        
        # 统计结果
        valid_results = [r for r in results if r is not None]
        success_count = sum(1 for r in valid_results if r['success'])
        
        print(f"\n📊 批量测试总结:")
        print(f"  总测试: {len(test_cases)}")
        print(f"  有效结果: {len(valid_results)}")
        print(f"  成功验证: {success_count}")
        print(f"  成功率: {success_count/len(valid_results)*100:.1f}%" if valid_results else "0%")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔧 测试修复后的增强ADK Agent")
    print("=" * 50)
    
    # 基础功能测试
    basic_success = await test_fixed_enhanced_agent()
    
    if basic_success:
        print("\n🎉 基础测试通过，继续批量测试...")
        batch_success = await test_multiple_questions()
        
        if batch_success:
            print("\n🎉 所有测试都通过了！")
            print("增强Agent现在可以正常工作了")
            
            print("\n💡 使用建议:")
            print("1. 现在可以在Jupyter Notebook中正常使用")
            print("2. 重新运行原始的enhanced_adk_agent.ipynb")
            print("3. 或者直接运行: python enhanced_adk_agent.py")
        else:
            print("\n⚠️ 批量测试部分失败，但基础功能正常")
    else:
        print("\n❌ 基础测试失败，需要进一步调试")
        print("\n🔧 可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 验证API Key是否有效")
        print("3. 确保所有依赖包正确安装")

if __name__ == "__main__":
    asyncio.run(main())
