{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔧 ADK Agent 诊断和修复\n", "\n", "这个Notebook帮助诊断和解决ADK Agent的问题。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import traceback\n", "\n", "print(\"🔍 环境检查开始...\")\n", "print(f\"Python版本: {sys.version}\")\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "\n", "# 检查必要的包\n", "required_packages = [\n", "    'asyncio',\n", "    'json', \n", "    'pydantic',\n", "    'litellm',\n", "    'google.adk',\n", "    'google.genai'\n", "]\n", "\n", "missing_packages = []\n", "for package in required_packages:\n", "    try:\n", "        __import__(package)\n", "        print(f\"✅ {package} - 已安装\")\n", "    except ImportError as e:\n", "        print(f\"❌ {package} - 未安装: {e}\")\n", "        missing_packages.append(package)\n", "\n", "if missing_packages:\n", "    print(f\"\\n⚠️ 缺少包: {missing_packages}\")\n", "    print(\"请运行: pip install google-adk litellm pydantic\")\n", "else:\n", "    print(\"\\n✅ 所有必要的包都已安装\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. API Key 检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查API Key\n", "api_key = os.environ.get(\"GEMINI_API_KEY\")\n", "\n", "if not api_key:\n", "    print(\"❌ GEMINI_API_KEY 环境变量未设置\")\n", "    print(\"请设置: os.environ['GEMINI_API_KEY'] = 'your_api_key'\")\n", "elif len(api_key) < 20:\n", "    print(\"⚠️ API Key 看起来太短，可能无效\")\n", "    print(f\"当前长度: {len(api_key)}\")\n", "else:\n", "    print(f\"✅ API Key 已设置 (长度: {len(api_key)})\")\n", "    print(f\"前缀: {api_key[:10]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 基础导入测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    print(\"🔄 测试基础导入...\")\n", "    \n", "    import asyncio\n", "    import json\n", "    from typing import Optional, List, Any, Dict\n", "    from datetime import datetime\n", "    print(\"✅ 标准库导入成功\")\n", "    \n", "    from pydantic import BaseModel, ValidationError, Field\n", "    print(\"✅ Pydantic导入成功\")\n", "    \n", "    from litellm import completion\n", "    print(\"✅ LiteLLM导入成功\")\n", "    \n", "    from google.adk.agents import Agent\n", "    from google.adk.models.lite_llm import LiteLlm\n", "    from google.adk.sessions import InMemorySessionService\n", "    from google.adk.runners import Runner\n", "    print(\"✅ Google ADK导入成功\")\n", "    \n", "    from google.genai import types\n", "    print(\"✅ Google GenAI导入成功\")\n", "    \n", "    print(\"\\n🎉 所有导入都成功！\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ 导入失败: {e}\")\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 简单的LiteLLM测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    print(\"🔄 测试LiteLLM连接...\")\n", "    \n", "    # 设置API Key（如果还没有设置）\n", "    if not os.environ.get(\"GEMINI_API_KEY\"):\n", "        os.environ[\"GEMINI_API_KEY\"] = \"AIzaSyCLNqIk7Y9_tMpSu_XfLp5s1egJ-WLdzOI\"\n", "    \n", "    from litellm import completion\n", "    \n", "    # 简单的API调用测试\n", "    response = completion(\n", "        model=\"gemini/gemini-2.5-flash\",\n", "        messages=[{\"role\": \"user\", \"content\": \"你好，请回答：1+1等于几？\"}],\n", "        max_tokens=50\n", "    )\n", "    \n", "    print(\"✅ LiteLLM API调用成功\")\n", "    print(f\"响应: {response.choices[0].message.content}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ LiteLLM测试失败: {e}\")\n", "    traceback.print_exc()\n", "    print(\"\\n💡 可能的解决方案:\")\n", "    print(\"1. 检查API Key是否正确\")\n", "    print(\"2. 检查网络连接\")\n", "    print(\"3. 检查API配额是否用完\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 测试Pydantic模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    print(\"🔄 测试Pydantic模型...\")\n", "    \n", "    from pydantic import BaseModel, Field\n", "    from typing import Optional\n", "    \n", "    class TestModel(BaseModel):\n", "        name: str = Field(description=\"姓名\")\n", "        age: Optional[int] = Field(None, description=\"年龄\")\n", "    \n", "    # 测试模型创建\n", "    test_data = TestModel(name=\"测试\", age=25)\n", "    print(f\"✅ 模型创建成功: {test_data.model_dump()}\")\n", "    \n", "    # 测试JSON schema\n", "    schema = test_data.model_json_schema()\n", "    print(f\"✅ JSON Schema生成成功\")\n", "    \n", "    # 测试验证\n", "    validated = TestModel.model_validate({\"name\": \"验证测试\", \"age\": 30})\n", "    print(f\"✅ 模型验证成功: {validated.model_dump()}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Pydantic测试失败: {e}\")\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 测试ADK Agent创建"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    print(\"🔄 测试ADK Agent创建...\")\n", "    \n", "    from google.adk.agents import Agent\n", "    from google.adk.models.lite_llm import LiteLlm\n", "    from google.adk.sessions import InMemorySessionService\n", "    from google.adk.runners import Runner\n", "    \n", "    # 创建模型\n", "    model = LiteLlm(model=\"gemini/gemini-2.5-flash\")\n", "    print(\"✅ LiteLlm模型创建成功\")\n", "    \n", "    # 创建Agent\n", "    agent = Agent(\n", "        name=\"test_agent\",\n", "        model=model,\n", "        instruction=\"你是一个测试助手，请简短回答问题。\",\n", "        description=\"测试Agent\"\n", "    )\n", "    print(\"✅ Agent创建成功\")\n", "    \n", "    # 创建会话服务\n", "    session_service = InMemorySessionService()\n", "    print(\"✅ 会话服务创建成功\")\n", "    \n", "    # 创建运行器\n", "    runner = Runner(\n", "        app_name=\"test_app\",\n", "        agent=agent,\n", "        session_service=session_service\n", "    )\n", "    print(\"✅ Runner创建成功\")\n", "    \n", "    print(\"\\n🎉 ADK组件创建都成功！\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ ADK Agent创建失败: {e}\")\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 测试简单对话"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def test_simple_chat():\n", "    try:\n", "        print(\"🔄 测试简单对话...\")\n", "        \n", "        from google.genai import types\n", "        \n", "        # 创建会话\n", "        await session_service.create_session(\n", "            app_name=\"test_app\",\n", "            user_id=\"test_user\",\n", "            session_id=\"test_session\"\n", "        )\n", "        print(\"✅ 会话创建成功\")\n", "        \n", "        # 创建用户消息\n", "        user_message = types.Content(\n", "            role='user',\n", "            parts=[types.Part(text=\"你好，1+1等于几？\")]\n", "        )\n", "        print(\"✅ 用户消息创建成功\")\n", "        \n", "        # 获取响应\n", "        response_text = \"\"\n", "        async for event in runner.run_async(\n", "            user_id=\"test_user\",\n", "            session_id=\"test_session\",\n", "            new_message=user_message\n", "        ):\n", "            if event.is_final_response() and event.content and event.content.parts:\n", "                response_text = event.content.parts[0].text\n", "                break\n", "        \n", "        if response_text:\n", "            print(f\"✅ 对话测试成功\")\n", "            print(f\"Agent回答: {response_text}\")\n", "            return True\n", "        else:\n", "            print(\"❌ 没有收到有效响应\")\n", "            return False\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ 对话测试失败: {e}\")\n", "        traceback.print_exc()\n", "        return False\n", "\n", "# 运行测试\n", "if 'runner' in locals() and 'session_service' in locals():\n", "    chat_success = await test_simple_chat()\n", "else:\n", "    print(\"❌ 请先运行上面的ADK组件创建测试\")\n", "    chat_success = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 诊断总结和建议"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"📊 诊断总结:\")\n", "print(\"=\" * 50)\n", "\n", "# 检查各项测试结果\n", "issues = []\n", "solutions = []\n", "\n", "# API Key检查\n", "if not os.environ.get(\"GEMINI_API_KEY\"):\n", "    issues.append(\"❌ API Key未设置\")\n", "    solutions.append(\"设置正确的Gemini API Key\")\n", "\n", "# 对话测试结果\n", "if 'chat_success' in locals() and not chat_success:\n", "    issues.append(\"❌ 对话测试失败\")\n", "    solutions.append(\"检查网络连接和API配额\")\n", "\n", "if not issues:\n", "    print(\"🎉 所有测试都通过了！\")\n", "    print(\"\\n💡 如果原始Notebook仍然失败，可能的原因:\")\n", "    print(\"1. 在<PERSON><PERSON><PERSON>中运行异步代码需要确保事件循环正确\")\n", "    print(\"2. 某些单元格可能需要重新运行\")\n", "    print(\"3. 尝试重启Jupyter内核并重新运行\")\n", "else:\n", "    print(\"发现以下问题:\")\n", "    for issue in issues:\n", "        print(f\"  {issue}\")\n", "    \n", "    print(\"\\n🔧 建议的解决方案:\")\n", "    for i, solution in enumerate(solutions, 1):\n", "        print(f\"  {i}. {solution}\")\n", "\n", "print(\"\\n📋 通用故障排除步骤:\")\n", "print(\"1. 重启Jupyter内核: Kernel -> Restart\")\n", "print(\"2. 重新安装包: !pip install --upgrade google-adk litellm pydantic\")\n", "print(\"3. 检查网络连接\")\n", "print(\"4. 验证API Key是否有效\")\n", "print(\"5. 检查API使用配额\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}