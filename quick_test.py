#!/usr/bin/env python3
"""
快速测试脚本 - 验证增强ADK Agent的核心功能
"""

import asyncio
import json
from enhanced_adk_agent import EnhancedADKAgent

async def test_question_classification():
    """测试问题分类功能"""
    print("🏷️  测试问题分类功能")
    print("-" * 40)
    
    agent = EnhancedADKAgent()
    
    test_cases = [
        ("我叫张三，今年25岁", "personal_info"),
        ("Python和Java有什么区别", "tech_comparison"),
        ("如何学习编程", "learning_advice"),
        ("今天天气怎么样", "general")
    ]
    
    for question, expected in test_cases:
        result = agent.classify_question(question)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{question}' -> {result}")
    
    print()

async def test_pydantic_models():
    """测试Pydantic模型验证"""
    print("📋 测试Pydantic模型验证")
    print("-" * 40)
    
    from enhanced_adk_agent import PersonInfo, TechnicalComparison, LearningAdvice, GeneralResponse
    from datetime import datetime
    
    # 测试PersonInfo
    try:
        person = PersonInfo(
            name="张三",
            age=25,
            occupation="软件工程师",
            location="北京"
        )
        print("✅ PersonInfo模型验证成功")
        print(f"   数据: {person.model_dump()}")
    except Exception as e:
        print(f"❌ PersonInfo模型验证失败: {e}")
    
    # 测试TechnicalComparison
    try:
        comparison = TechnicalComparison(
            technology1="Python",
            technology2="Java",
            differences=["语法不同", "运行方式不同"],
            advantages={
                "Python": ["简洁", "易学"],
                "Java": ["性能好", "企业级"]
            },
            use_cases={
                "Python": ["数据科学", "脚本"],
                "Java": ["企业应用", "Android开发"]
            }
        )
        print("✅ TechnicalComparison模型验证成功")
        print(f"   技术1: {comparison.technology1}, 技术2: {comparison.technology2}")
    except Exception as e:
        print(f"❌ TechnicalComparison模型验证失败: {e}")
    
    print()

async def test_single_interaction():
    """测试单次交互"""
    print("🤖 测试单次交互")
    print("-" * 40)
    
    try:
        agent = EnhancedADKAgent()
        
        # 使用一个简单的问题进行测试
        test_question = "我叫李四，是一名教师"
        print(f"测试问题: {test_question}")
        
        # 只测试分类，不进行实际的API调用
        question_type = agent.classify_question(test_question)
        print(f"✅ 问题分类: {question_type}")
        
        # 获取对应的模型类
        target_model = agent.response_models[question_type]
        print(f"✅ 目标模型: {target_model.__name__}")
        
        # 显示模型结构
        schema = target_model.model_json_schema()
        print(f"✅ 模型字段: {list(schema.get('properties', {}).keys())}")
        
    except Exception as e:
        print(f"❌ 交互测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print()

def test_model_schemas():
    """测试模型结构"""
    print("🏗️  测试模型结构")
    print("-" * 40)
    
    from enhanced_adk_agent import PersonInfo, TechnicalComparison, LearningAdvice, GeneralResponse
    
    models = [
        ("PersonInfo", PersonInfo),
        ("TechnicalComparison", TechnicalComparison),
        ("LearningAdvice", LearningAdvice),
        ("GeneralResponse", GeneralResponse)
    ]
    
    for name, model_class in models:
        try:
            schema = model_class.model_json_schema()
            fields = list(schema.get('properties', {}).keys())
            required = schema.get('required', [])
            
            print(f"✅ {name}:")
            print(f"   字段: {fields}")
            print(f"   必需: {required}")
            
        except Exception as e:
            print(f"❌ {name} 结构测试失败: {e}")
    
    print()

async def main():
    """主测试函数"""
    print("🧪 增强ADK Agent快速测试")
    print("=" * 50)
    
    try:
        # 测试模型结构
        test_model_schemas()
        
        # 测试问题分类
        await test_question_classification()
        
        # 测试Pydantic模型
        await test_pydantic_models()
        
        # 测试单次交互（不调用API）
        await test_single_interaction()
        
        print("🎉 所有基础测试通过!")
        print("💡 如需完整测试（包括API调用），请运行:")
        print("   python demo.py")
        print("   python test_enhanced_agent.py")
        
    except Exception as e:
        print(f"💥 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
