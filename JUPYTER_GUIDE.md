# Jupyter Notebook 使用指南

## 📚 文件说明

我为你创建了两个Jupyter Notebook版本：

### 1. `enhanced_adk_complete.ipynb` - 完整版
- **用途**: 完整的教学和演示版本
- **内容**: 包含所有代码定义、详细说明和测试
- **适合**: 学习理解整个系统的工作原理

### 2. `quick_test_notebook.ipynb` - 快速测试版
- **用途**: 快速测试和验证功能
- **内容**: 简化的测试流程，专注于结果
- **适合**: 日常使用和快速验证

## 🚀 快速开始

### 方法1: 使用快速测试版（推荐）

1. **打开Notebook**
   ```bash
   jupyter notebook quick_test_notebook.ipynb
   ```

2. **运行所有单元格**
   - 点击 `Cell` -> `Run All`
   - 或者按 `Shift + Enter` 逐个运行

3. **修改测试问题**
   - 在测试单元格中修改问题变量
   - 重新运行对应的单元格

### 方法2: 使用完整版

1. **打开完整版Notebook**
   ```bash
   jupyter notebook enhanced_adk_complete.ipynb
   ```

2. **按顺序运行单元格**
   - 从第1个单元格开始
   - 逐步运行到第8个单元格（实际测试）

## 📋 使用步骤详解

### Step 1: 环境准备
```python
# 确保安装了必要的包
!pip install google-adk litellm pydantic jupyter
```

### Step 2: 设置API Key
在Notebook的第2个单元格中设置你的API Key：
```python
os.environ["GEMINI_API_KEY"] = "your_api_key_here"
```

### Step 3: 运行基础测试
运行问题分类和模型验证测试，确保基础功能正常。

### Step 4: 实际对话测试
修改测试问题并运行，查看结构化输出结果。

## 🎯 测试示例

### 个人信息提取
```python
question = "我叫张三，今年25岁，是一名软件工程师，在北京工作"
result = await test_agent(question)
```

**期望输出**:
```json
{
  "name": "张三",
  "age": 25,
  "occupation": "软件工程师",
  "location": "北京"
}
```

### 技术比较
```python
question = "Python和Java有什么主要区别？"
result = await test_agent(question)
```

**期望输出**:
```json
{
  "technology1": "Python",
  "technology2": "Java",
  "differences": ["语法简洁性不同", "运行方式不同"],
  "advantages": {
    "Python": ["简洁易读", "丰富的库"],
    "Java": ["性能稳定", "企业级支持"]
  },
  "use_cases": {
    "Python": ["数据科学", "机器学习"],
    "Java": ["企业应用", "Android开发"]
  }
}
```

### 学习建议
```python
question = "如何学习机器学习？"
result = await test_agent(question)
```

**期望输出**:
```json
{
  "topic": "机器学习",
  "beginner_tips": ["学习Python基础", "了解数学基础"],
  "intermediate_tips": ["实践项目", "深入算法"],
  "resources": ["在线课程", "实践平台"],
  "timeline": "6-12个月"
}
```

## 🔧 自定义和扩展

### 添加新的问题类型

1. **定义新的Pydantic模型**
```python
class NewDataModel(BaseModel):
    field1: str = Field(description="字段1")
    field2: List[str] = Field(description="字段2")
```

2. **更新问题分类逻辑**
```python
def classify_question(self, question: str) -> str:
    # 添加新的关键词检测
    if "新关键词" in question.lower():
        return "new_type"
    # ... 其他逻辑
```

3. **注册新模型**
```python
self.response_models["new_type"] = NewDataModel
```

### 修改验证逻辑

可以在 `chat_with_validation` 方法中调整：
- 最大重试次数
- 验证失败的处理方式
- 错误信息的格式

## 📊 结果分析

### 验证成功率
系统会显示每次测试的验证结果：
- ✅ 成功: 输出符合预期格式
- ❌ 失败: 需要人工检查

### 尝试次数
- 1次: 第一次就成功
- 2-3次: 经过修复后成功
- 超过3次: 可能需要调整模型或问题

### 问题类型分布
系统会自动统计不同类型问题的处理情况。

## 🐛 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'enhanced_adk_agent'
   ```
   **解决**: 确保 `enhanced_adk_agent.py` 在当前目录

2. **API错误**
   ```
   Authentication failed
   ```
   **解决**: 检查API Key是否正确设置

3. **验证失败**
   ```
   数据提取失败: ValidationError
   ```
   **解决**: 这是正常现象，系统会自动重试

### 调试技巧

1. **查看原始响应**
   ```python
   print(f"原始回答: {result['response']}")
   ```

2. **检查问题分类**
   ```python
   category = agent.classify_question(question)
   print(f"问题类型: {category}")
   ```

3. **查看模型结构**
   ```python
   model = agent.response_models[category]
   print(f"模型字段: {list(model.model_fields.keys())}")
   ```

## 💡 最佳实践

### 1. 问题设计
- **明确具体**: 避免过于模糊的问题
- **信息完整**: 提供足够的上下文信息
- **类型明确**: 确保问题属于支持的类型

### 2. 结果验证
- **检查完整性**: 确保所有必需字段都有值
- **验证准确性**: 检查提取的信息是否正确
- **格式一致性**: 确保输出格式符合预期

### 3. 性能优化
- **批量测试**: 使用批量测试功能提高效率
- **缓存结果**: 对于相同问题可以缓存结果
- **错误处理**: 妥善处理API调用失败的情况

## 🎯 下一步计划

1. **扩展数据模型**: 添加更多业务场景的数据模型
2. **优化分类算法**: 使用机器学习提高分类准确性
3. **集成更多模型**: 支持其他LLM提供商
4. **添加Web界面**: 创建用户友好的Web界面
5. **部署到云端**: 支持云端部署和API服务

## 📞 支持

如果遇到问题或需要帮助：
1. 检查本指南的故障排除部分
2. 查看代码注释和文档
3. 运行基础测试验证环境
4. 检查API Key和网络连接

祝你使用愉快！🎉
