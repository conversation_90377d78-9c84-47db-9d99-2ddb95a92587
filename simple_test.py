#!/usr/bin/env python3
"""
简单的ADK Agent测试脚本
用于快速诊断和验证基础功能
"""

import asyncio
import os
import sys
import traceback
import json
from typing import Optional, List, Any, Dict
from datetime import datetime

def check_environment():
    """检查环境和依赖"""
    print("🔍 环境检查...")
    print(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'pydantic',
        'litellm', 
        'google.adk',
        'google.genai'
    ]
    
    missing = []
    for pkg in required_packages:
        try:
            __import__(pkg)
            print(f"✅ {pkg}")
        except ImportError:
            print(f"❌ {pkg} - 未安装")
            missing.append(pkg)
    
    if missing:
        print(f"\n⚠️ 请安装缺少的包:")
        print("pip install google-adk litellm pydantic")
        return False
    
    return True

def check_api_key():
    """检查API Key"""
    print("\n🔑 API Key检查...")
    
    # 设置API Key
    os.environ["GEMINI_API_KEY"] = "AIzaSyCLNqIk7Y9_tMpSu_XfLp5s1egJ-WLdzOI"
    
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        print("❌ API Key未设置")
        return False
    
    print(f"✅ API Key已设置 (长度: {len(api_key)})")
    return True

async def test_litellm():
    """测试LiteLLM基础功能"""
    print("\n🔄 测试LiteLLM...")
    
    try:
        from litellm import completion
        
        response = completion(
            model="gemini/gemini-2.5-flash",
            messages=[{"role": "user", "content": "你好，请回答：1+1等于几？"}],
            max_tokens=50
        )
        
        result = response.choices[0].message.content
        print(f"✅ LiteLLM测试成功: {result}")
        return True
        
    except Exception as e:
        print(f"❌ LiteLLM测试失败: {e}")
        return False

def test_pydantic():
    """测试Pydantic模型"""
    print("\n📋 测试Pydantic...")
    
    try:
        from pydantic import BaseModel, Field
        
        class TestModel(BaseModel):
            name: str = Field(description="姓名")
            age: Optional[int] = Field(None, description="年龄")
        
        # 测试创建
        test_data = TestModel(name="测试", age=25)
        print(f"✅ 模型创建: {test_data.model_dump()}")
        
        # 测试验证
        validated = TestModel.model_validate({"name": "验证", "age": 30})
        print(f"✅ 模型验证: {validated.model_dump()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pydantic测试失败: {e}")
        return False

async def test_adk_agent():
    """测试ADK Agent"""
    print("\n🤖 测试ADK Agent...")
    
    try:
        from google.adk.agents import Agent
        from google.adk.models.lite_llm import LiteLlm
        from google.adk.sessions import InMemorySessionService
        from google.adk.runners import Runner
        from google.genai import types
        
        # 创建组件
        model = LiteLlm(model="gemini/gemini-2.5-flash")
        print("✅ 模型创建成功")
        
        agent = Agent(
            name="test_agent",
            model=model,
            instruction="你是一个测试助手，请简短回答。",
            description="测试Agent"
        )
        print("✅ Agent创建成功")
        
        session_service = InMemorySessionService()
        runner = Runner(
            app_name="test_app",
            agent=agent,
            session_service=session_service
        )
        print("✅ Runner创建成功")
        
        # 测试对话
        await session_service.create_session(
            app_name="test_app",
            user_id="test_user",
            session_id="test_session"
        )
        
        user_message = types.Content(
            role='user',
            parts=[types.Part(text="你好，1+1等于几？")]
        )
        
        response_text = ""
        async for event in runner.run_async(
            user_id="test_user",
            session_id="test_session", 
            new_message=user_message
        ):
            if event.is_final_response() and event.content and event.content.parts:
                response_text = event.content.parts[0].text
                break
        
        if response_text:
            print(f"✅ 对话测试成功: {response_text}")
            return True
        else:
            print("❌ 没有收到响应")
            return False
            
    except Exception as e:
        print(f"❌ ADK Agent测试失败: {e}")
        traceback.print_exc()
        return False

async def test_enhanced_agent():
    """测试增强Agent的核心功能"""
    print("\n🚀 测试增强Agent...")
    
    try:
        # 导入增强Agent
        sys.path.append('.')
        from enhanced_adk_agent import EnhancedADKAgent
        
        # 创建实例
        agent = EnhancedADKAgent()
        print("✅ 增强Agent创建成功")
        
        # 测试问题分类
        test_questions = [
            ("我叫张三，25岁", "personal_info"),
            ("Python和Java的区别", "tech_comparison"),
            ("如何学习编程", "learning_advice"),
            ("今天天气", "general")
        ]
        
        print("\n🏷️ 测试问题分类:")
        all_correct = True
        for question, expected in test_questions:
            result = agent.classify_question(question)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{question}' -> {result}")
            if result != expected:
                all_correct = False
        
        if all_correct:
            print("✅ 问题分类测试通过")
        else:
            print("⚠️ 问题分类有误差")
        
        # 测试简单对话
        print("\n💬 测试简单对话:")
        test_question = "我叫李明，30岁，软件工程师"
        result = await agent.chat_with_validation(test_question)
        
        if result['success']:
            print("✅ 对话验证成功")
            print(f"问题类型: {result['question_type']}")
            print(f"尝试次数: {result['attempts']}")
            if result['structured_data']:
                print("✅ 结构化数据提取成功")
                print(json.dumps(result['structured_data'], ensure_ascii=False, indent=2))
        else:
            print(f"❌ 对话验证失败: {result.get('error', '未知错误')}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ 增强Agent测试失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🔧 ADK Agent 诊断测试")
    print("=" * 50)
    
    # 环境检查
    if not check_environment():
        print("\n❌ 环境检查失败，请安装必要的包")
        return
    
    # API Key检查
    if not check_api_key():
        print("\n❌ API Key检查失败")
        return
    
    # 基础功能测试
    tests = [
        ("LiteLLM", test_litellm()),
        ("Pydantic", test_pydantic()),
        ("ADK Agent", test_adk_agent()),
        ("增强Agent", test_enhanced_agent())
    ]
    
    results = {}
    for name, test_coro in tests:
        try:
            if asyncio.iscoroutine(test_coro):
                result = await test_coro
            else:
                result = test_coro
            results[name] = result
        except Exception as e:
            print(f"❌ {name}测试异常: {e}")
            results[name] = False
    
    # 总结
    print("\n📊 测试总结:")
    print("=" * 30)
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for name, result in results.items():
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试都通过了！")
        print("你的环境配置正确，可以正常使用ADK Agent")
    else:
        print("\n⚠️ 部分测试失败")
        print("请检查失败的组件并按照错误信息进行修复")

if __name__ == "__main__":
    asyncio.run(main())
