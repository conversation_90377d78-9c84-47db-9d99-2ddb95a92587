{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 增强的Google ADK Agent with Pydantic验证\n", "\n", "这个Notebook展示了如何使用Google ADK构建一个带有Pydantic验证和自动修复功能的AI代理。\n", "\n", "## 主要特性\n", "- 🎯 智能问题分类\n", "- 📊 结构化数据提取\n", "- 🔧 自动验证和修复\n", "- 📋 多种数据模型支持"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 安装和导入依赖"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的包（如果还没有安装）\n", "# !pip install google-adk litellm pydantic"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 所有依赖导入成功！\n"]}], "source": ["import asyncio\n", "import json\n", "import os\n", "from typing import Optional, List, Any, Dict\n", "from datetime import datetime\n", "\n", "# Google ADK imports\n", "from litellm import completion\n", "from google.adk.agents import Agent\n", "from google.adk.models.lite_llm import LiteLlm\n", "from google.adk.sessions import InMemorySessionService\n", "from google.adk.runners import Runner\n", "from google.genai import types\n", "\n", "# Pydantic imports\n", "from pydantic import BaseModel, ValidationError, Field\n", "\n", "print(\"✅ 所有依赖导入成功！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 设置API Key"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔑 API Key 设置完成\n"]}], "source": ["# 设置你的Gemini API Key\n", "os.environ[\"GEMINI_API_KEY\"] = \"AIzaSyCLNqIk7Y9_tMpSu_XfLp5s1egJ-WLdzOI\"\n", "\n", "print(\"🔑 API Key 设置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 定义Pydantic数据模型"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 Pydantic模型定义完成\n", "PersonInfo字段: ['name', 'age', 'occupation', 'location']\n", "TechnicalComparison字段: ['technology1', 'technology2', 'differences', 'advantages', 'use_cases']\n"]}], "source": ["class PersonInfo(BaseModel):\n", "    \"\"\"个人信息模型\"\"\"\n", "    name: str = Field(description=\"姓名\")\n", "    age: Optional[int] = Field(None, description=\"年龄\", ge=0, le=150)\n", "    occupation: Optional[str] = Field(None, description=\"职业\")\n", "    location: Optional[str] = Field(None, description=\"地点\")\n", "\n", "class TechnicalComparison(BaseModel):\n", "    \"\"\"技术比较模型\"\"\"\n", "    technology1: str = Field(description=\"第一个技术\")\n", "    technology2: str = Field(description=\"第二个技术\")\n", "    differences: List[str] = Field(description=\"主要区别列表\")\n", "    advantages: Dict[str, List[str]] = Field(description=\"各自优势\")\n", "    use_cases: Dict[str, List[str]] = Field(description=\"使用场景\")\n", "\n", "class LearningAdvice(BaseModel):\n", "    \"\"\"学习建议模型\"\"\"\n", "    topic: str = Field(description=\"学习主题\")\n", "    beginner_tips: List[str] = Field(description=\"初学者建议\")\n", "    intermediate_tips: List[str] = Field(description=\"进阶建议\")\n", "    resources: List[str] = Field(description=\"推荐资源\")\n", "    timeline: Optional[str] = Field(None, description=\"学习时间线\")\n", "\n", "class GeneralResponse(BaseModel):\n", "    \"\"\"通用响应模型\"\"\"\n", "    content: str = Field(description=\"回答内容\")\n", "    category: str = Field(description=\"回答类别\")\n", "    confidence: Optional[float] = Field(None, description=\"置信度\", ge=0.0, le=1.0)\n", "    timestamp: datetime = Field(default_factory=datetime.now, description=\"时间戳\")\n", "\n", "print(\"📋 Pydantic模型定义完成\")\n", "print(f\"PersonInfo字段: {list(PersonInfo.model_fields.keys())}\")\n", "print(f\"TechnicalComparison字段: {list(TechnicalComparison.model_fields.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 创建增强的ADK Agent类"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 EnhancedADKAgent类定义完成\n"]}], "source": ["class EnhancedADKAgent:\n", "    def __init__(self):\n", "        # 创建 LLM 模型实例\n", "        self.model = LiteLlm(model=\"gemini/gemini-2.5-flash\")\n", "        \n", "        # 创建主要的对话Agent\n", "        self.main_agent = Agent(\n", "            name=\"enhanced_assistant\",\n", "            model=self.model,\n", "            instruction=\"\"\"\n", "            你是一个专业的AI助手。请根据用户的问题类型，提供结构化的回答：\n", "            \n", "            1. 如果是个人介绍相关，请提供姓名、年龄、职业、地点等信息\n", "            2. 如果是技术比较，请详细对比两个技术的区别、优势和使用场景\n", "            3. 如果是学习建议，请提供分层次的建议和资源推荐\n", "            4. 其他问题请提供通用格式的回答\n", "            \n", "            请用中文回答，并尽量提供结构化的信息。\n", "            \"\"\",\n", "            description=\"增强的中文对话助手，支持结构化输出\"\n", "        )\n", "        \n", "        # 创建修复Agent\n", "        self.fix_agent = Agent(\n", "            name=\"fix_assistant\", \n", "            model=self.model,\n", "            instruction=\"\"\"\n", "            你是一个专门负责修复和完善回答的AI助手。\n", "            当收到一个不完整或格式不正确的回答时，请：\n", "            1. 分析缺失的信息\n", "            2. 补充必要的细节\n", "            3. 确保回答符合要求的结构化格式\n", "            4. 保持回答的准确性和完整性\n", "            \n", "            请用中文回答。\n", "            \"\"\",\n", "            description=\"回答修复助手\"\n", "        )\n", "        \n", "        # 初始化会话服务和运行器\n", "        self.session_service = InMemorySessionService()\n", "        self.main_runner = Runner(\n", "            app_name=\"enhanced_chat_app\",\n", "            agent=self.main_agent,\n", "            session_service=self.session_service\n", "        )\n", "        self.fix_runner = Runner(\n", "            app_name=\"fix_chat_app\", \n", "            agent=self.fix_agent,\n", "            session_service=self.session_service\n", "        )\n", "        \n", "        # 定义问题类型和对应的Pydantic模型\n", "        self.response_models = {\n", "            \"personal_info\": PersonInfo,\n", "            \"tech_comparison\": TechnicalComparison,\n", "            \"learning_advice\": LearningAdvice,\n", "            \"general\": GeneralResponse\n", "        }\n", "\n", "    def classify_question(self, question: str) -> str:\n", "        \"\"\"分类用户问题\"\"\"\n", "        question_lower = question.lower()\n", "        \n", "        # 个人信息关键词检测\n", "        personal_keywords = [\"介绍\", \"我是\", \"我叫\", \"姓名\", \"年龄\", \"职业\", \"岁\", \"工作\", \"住在\"]\n", "        if any(keyword in question_lower for keyword in personal_keywords):\n", "            return \"personal_info\"\n", "        \n", "        # 技术比较关键词检测\n", "        tech_keywords = [\"区别\", \"比较\", \"差异\", \"vs\", \"对比\", \"不同\", \"哪个好\", \"优缺点\"]\n", "        if any(keyword in question_lower for keyword in tech_keywords):\n", "            return \"tech_comparison\"\n", "        \n", "        # 学习建议关键词检测\n", "        learning_keywords = [\"学习\", \"建议\", \"推荐\", \"如何学\", \"怎么学\", \"教程\", \"入门\", \"指导\"]\n", "        if any(keyword in question_lower for keyword in learning_keywords):\n", "            return \"learning_advice\"\n", "        \n", "        return \"general\"\n", "\n", "print(\"🤖 EnhancedADKAgent类定义完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 添加核心方法"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 核心方法添加完成\n"]}], "source": ["# 为EnhancedADKAgent类添加核心方法\n", "\n", "async def get_agent_response(self, user_input: str, runner: <PERSON>, user_id: str = \"user_001\", session_id: str = \"session_001\") -> str:\n", "    \"\"\"获取Agent响应\"\"\"\n", "    user_message = types.Content(\n", "        role='user',\n", "        parts=[types.Part(text=user_input)]\n", "    )\n", "    \n", "    final_response = \"\"\n", "    async for event in runner.run_async(\n", "        user_id=user_id,\n", "        session_id=session_id,\n", "        new_message=user_message\n", "    ):\n", "        if event.is_final_response() and event.content and event.content.parts:\n", "            final_response = event.content.parts[0].text\n", "            break\n", "    \n", "    return final_response\n", "\n", "async def extract_structured_data(self, response: str, model_class: BaseModel) -> Optional[BaseModel]:\n", "    \"\"\"尝试从响应中提取结构化数据\"\"\"\n", "    try:\n", "        # 尝试直接解析JSON\n", "        if response.strip().startswith('{'):\n", "            data = json.loads(response)\n", "            return model_class.model_validate(data)\n", "        \n", "        # 如果不是JSON，使用LLM提取结构化信息\n", "        extraction_prompt = f\"\"\"\n", "        请从以下文本中提取信息，并严格按照JSON格式返回，符合以下结构：\n", "        \n", "        JSON Schema:\n", "        {json.dumps(model_class.model_json_schema(), ensure_ascii=False, indent=2)}\n", "        \n", "        文本内容：\n", "        {response}\n", "        \n", "        要求：\n", "        1. 只返回有效的JSON格式\n", "        2. 确保所有必需字段都有值\n", "        3. 不要添加任何其他文字说明\n", "        4. 如果某些信息在文本中没有，请合理推断或使用null\n", "        \n", "        JSON:\n", "        \"\"\"\n", "        \n", "        # 使用extraction agent来提取结构化数据\n", "        extraction_response = await self.get_agent_response(\n", "            extraction_prompt, \n", "            self.fix_runner,\n", "            user_id=\"extractor_001\",\n", "            session_id=\"extract_001\"\n", "        )\n", "        \n", "        # 清理响应，只保留JSON部分\n", "        json_start = extraction_response.find('{')\n", "        json_end = extraction_response.rfind('}') + 1\n", "        \n", "        if json_start != -1 and json_end > json_start:\n", "            json_str = extraction_response[json_start:json_end]\n", "            data = json.loads(json_str)\n", "            return model_class.model_validate(data)\n", "        \n", "        return None\n", "        \n", "    except (json.J<PERSON>rror, ValidationError) as e:\n", "        print(f\"数据提取失败: {e}\")\n", "        return None\n", "\n", "# 将方法绑定到类\n", "EnhancedADKAgent.get_agent_response = get_agent_response\n", "EnhancedADKAgent.extract_structured_data = extract_structured_data\n", "\n", "print(\"🔧 核心方法添加完成\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10", "language": "python", "name": "py310"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}