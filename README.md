# 增强的Google ADK Agent with Pydantic验证

这个项目基于Google ADK (Agent Development Kit) 构建了一个增强的AI代理，集成了Pydantic数据验证功能，能够自动提取和验证结构化数据，并在验证失败时自动修复响应。

## 主要特性

### 🎯 智能问题分类
- **个人信息**: 自动识别和提取姓名、年龄、职业、地点等信息
- **技术比较**: 结构化对比两个技术的区别、优势和使用场景
- **学习建议**: 分层次提供学习建议和资源推荐
- **通用问题**: 处理其他类型的问题

### 🔧 自动修复机制
- 使用Pydantic模型验证输出格式
- 验证失败时自动调用修复Agent
- 最多重试2次确保输出质量
- 智能提取JSON格式的结构化数据

### 📊 结构化输出
所有响应都会尝试转换为结构化数据，便于后续处理和分析。

## 安装依赖

```bash
pip install google-adk litellm pydantic
```

## 使用方法

### 基本使用

```python
import asyncio
from enhanced_adk_agent import EnhancedADKAgent

async def main():
    # 创建增强的Agent实例
    agent = EnhancedADKAgent()
    
    # 处理用户问题
    result = await agent.chat_with_validation("Python和JavaScript有什么区别？")
    
    # 查看结果
    print(f"回答: {result['response']}")
    print(f"验证成功: {result['success']}")
    print(f"结构化数据: {result['structured_data']}")

asyncio.run(main())
```

### 运行测试

```bash
# 运行全面测试
python test_enhanced_agent.py

# 运行交互式测试
python test_enhanced_agent.py interactive
```

## 数据模型

### PersonInfo (个人信息)
```python
{
    "name": "张三",
    "age": 25,
    "occupation": "软件工程师", 
    "location": "北京"
}
```

### TechnicalComparison (技术比较)
```python
{
    "technology1": "Python",
    "technology2": "JavaScript",
    "differences": ["语法差异", "运行环境不同"],
    "advantages": {
        "Python": ["简洁易读", "丰富的库"],
        "JavaScript": ["前端支持", "异步处理"]
    },
    "use_cases": {
        "Python": ["数据科学", "后端开发"],
        "JavaScript": ["前端开发", "Node.js后端"]
    }
}
```

### LearningAdvice (学习建议)
```python
{
    "topic": "机器学习",
    "beginner_tips": ["学习Python基础", "了解数学基础"],
    "intermediate_tips": ["实践项目", "深入算法"],
    "resources": ["Coursera课程", "Kaggle竞赛"],
    "timeline": "6-12个月"
}
```

### GeneralResponse (通用响应)
```python
{
    "content": "回答内容",
    "category": "通用问题",
    "confidence": 0.9,
    "timestamp": "2024-01-01T12:00:00"
}
```

## 工作流程

1. **问题分类**: 根据关键词自动识别问题类型
2. **初始响应**: 主Agent生成初始回答
3. **数据提取**: 尝试从回答中提取结构化数据
4. **验证检查**: 使用Pydantic模型验证数据格式
5. **自动修复**: 如果验证失败，调用修复Agent重新生成
6. **返回结果**: 返回验证后的结构化数据

## 配置说明

### API Key设置
在代码中设置你的Gemini API Key：
```python
os.environ["GEMINI_API_KEY"] = "your_api_key_here"
```

### 模型配置
默认使用 `gemini/gemini-2.5-flash` 模型，可以根据需要修改：
```python
self.model = LiteLlm(model="gemini/gemini-2.5-flash")
```

## 扩展功能

### 添加新的数据模型
1. 在代码中定义新的Pydantic模型
2. 在 `response_models` 字典中注册
3. 在 `classify_question` 方法中添加分类逻辑

### 自定义验证规则
可以在Pydantic模型中添加自定义验证器：
```python
from pydantic import validator

class CustomModel(BaseModel):
    value: int
    
    @validator('value')
    def validate_value(cls, v):
        if v < 0:
            raise ValueError('值必须为正数')
        return v
```

## 注意事项

1. **API限制**: 注意Gemini API的调用限制和费用
2. **重试机制**: 默认最多重试2次，可根据需要调整
3. **数据隐私**: 确保不在日志中泄露敏感信息
4. **错误处理**: 建议在生产环境中添加更完善的错误处理

## 示例输出

```
问题类型: tech_comparison
目标模型: TechnicalComparison

--- 尝试 1 ---
Agent响应: Python和JavaScript是两种不同的编程语言...

✅ 验证成功!

📋 结构化数据:
{
    "technology1": "Python",
    "technology2": "JavaScript", 
    "differences": [
        "Python是解释型语言，JavaScript主要用于前端",
        "Python语法更简洁，JavaScript语法更灵活"
    ],
    "advantages": {
        "Python": ["易学易用", "丰富的第三方库", "强大的数据处理能力"],
        "JavaScript": ["前端开发必备", "异步编程支持", "生态系统庞大"]
    },
    "use_cases": {
        "Python": ["数据科学", "机器学习", "后端开发", "自动化脚本"],
        "JavaScript": ["前端开发", "Node.js后端", "移动应用开发"]
    }
}
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
