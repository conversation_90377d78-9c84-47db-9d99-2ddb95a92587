#!/usr/bin/env python3
"""
最终测试脚本 - 验证所有功能都正常工作
"""

import asyncio
import os
import sys
import json

# 设置API Key
os.environ["GEMINI_API_KEY"] = "AIzaSyCLNqIk7Y9_tMpSu_XfLp5s1egJ-WLdzOI"

async def comprehensive_test():
    """全面测试增强Agent的所有功能"""
    print("🎯 全面功能测试")
    print("=" * 60)
    
    try:
        from enhanced_adk_agent import EnhancedADKAgent
        
        # 创建Agent实例
        agent = EnhancedADKAgent()
        print("✅ Agent创建成功")
        
        # 测试用例
        test_cases = [
            {
                "question": "我叫张伟，28岁，是一名数据科学家，在深圳工作",
                "type": "personal_info",
                "description": "个人信息提取测试"
            },
            {
                "question": "Python和R语言在数据分析方面有什么区别？请详细比较",
                "type": "tech_comparison", 
                "description": "技术比较测试"
            },
            {
                "question": "我想学习深度学习，能给我一个系统的学习计划吗？",
                "type": "learning_advice",
                "description": "学习建议测试"
            },
            {
                "question": "什么是量子计算？",
                "type": "general",
                "description": "通用问题测试"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'='*60}")
            print(f"测试 {i}/4: {test_case['description']}")
            print(f"问题: {test_case['question']}")
            print("-" * 60)
            
            try:
                # 执行对话
                result = await agent.chat_with_validation(test_case['question'])
                results.append(result)
                
                # 显示结果
                print(f"\n📊 测试结果:")
                print(f"  ✅ 成功: {'是' if result['success'] else '否'}")
                print(f"  🏷️ 问题类型: {result['question_type']} (期望: {test_case['type']})")
                print(f"  🔄 尝试次数: {result['attempts']}")
                
                if result['success']:
                    print(f"  📋 结构化数据提取: ✅")
                    if result['structured_data']:
                        # 显示结构化数据的关键信息
                        data = result['structured_data']
                        if test_case['type'] == 'personal_info':
                            print(f"    姓名: {data.get('name', 'N/A')}")
                            print(f"    年龄: {data.get('age', 'N/A')}")
                            print(f"    职业: {data.get('occupation', 'N/A')}")
                        elif test_case['type'] == 'tech_comparison':
                            print(f"    技术1: {data.get('technology1', 'N/A')}")
                            print(f"    技术2: {data.get('technology2', 'N/A')}")
                            print(f"    区别数量: {len(data.get('differences', []))}")
                        elif test_case['type'] == 'learning_advice':
                            print(f"    学习主题: {data.get('topic', 'N/A')}")
                            print(f"    初学者建议数: {len(data.get('beginner_tips', []))}")
                            print(f"    进阶建议数: {len(data.get('intermediate_tips', []))}")
                        elif test_case['type'] == 'general':
                            print(f"    内容长度: {len(data.get('content', ''))}")
                            print(f"    类别: {data.get('category', 'N/A')}")
                else:
                    print(f"  ❌ 失败原因: {result.get('error', '未知')}")
                
                print(f"  💬 回答预览: {result['response'][:100]}...")
                
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                results.append(None)
        
        # 统计总结
        print(f"\n{'='*60}")
        print("📊 测试总结")
        print("=" * 60)
        
        valid_results = [r for r in results if r is not None]
        success_count = sum(1 for r in valid_results if r['success'])
        
        print(f"总测试数: {len(test_cases)}")
        print(f"有效结果: {len(valid_results)}")
        print(f"成功验证: {success_count}")
        print(f"成功率: {success_count/len(valid_results)*100:.1f}%" if valid_results else "0%")
        
        # 按类型统计
        type_stats = {}
        for result in valid_results:
            if result['success']:
                qtype = result['question_type']
                type_stats[qtype] = type_stats.get(qtype, 0) + 1
        
        print(f"\n按类型成功统计:")
        for qtype, count in type_stats.items():
            print(f"  {qtype}: {count}")
        
        # 平均尝试次数
        if valid_results:
            avg_attempts = sum(r['attempts'] for r in valid_results) / len(valid_results)
            print(f"\n平均尝试次数: {avg_attempts:.1f}")
        
        # 最终评估
        if success_count == len(test_cases):
            print(f"\n🎉 完美！所有测试都通过了！")
            print("增强ADK Agent已经完全正常工作")
            return True
        elif success_count >= len(test_cases) * 0.75:
            print(f"\n✅ 很好！大部分测试通过了")
            print("增强ADK Agent基本正常工作")
            return True
        else:
            print(f"\n⚠️ 需要改进，部分测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 增强ADK Agent - 最终验证测试")
    print("这将测试所有核心功能，包括:")
    print("- 问题分类")
    print("- 结构化数据提取") 
    print("- 自动验证和重试")
    print("- 多种数据模型")
    print()
    
    success = await comprehensive_test()
    
    if success:
        print(f"\n🎯 测试结论: 系统完全正常！")
        print(f"\n💡 现在你可以:")
        print("1. 在Jupyter Notebook中使用: jupyter notebook enhanced_adk_agent.ipynb")
        print("2. 直接运行Python脚本: python enhanced_adk_agent.py")
        print("3. 集成到你的项目中")
        print("4. 根据需要扩展更多功能")
    else:
        print(f"\n⚠️ 测试结论: 需要进一步调试")
        print("请检查错误信息并进行相应修复")

if __name__ == "__main__":
    asyncio.run(main())
