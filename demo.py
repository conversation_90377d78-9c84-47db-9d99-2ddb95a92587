#!/usr/bin/env python3
"""
增强ADK Agent演示脚本
展示Pydantic验证和自动修复功能
"""

import asyncio
import json
from enhanced_adk_agent import EnhancedADKAgent

async def demo_basic_functionality():
    """演示基本功能"""
    print("🚀 增强ADK Agent演示")
    print("=" * 60)
    
    # 创建Agent实例
    agent = EnhancedADKAgent()
    
    # 演示问题列表
    demo_questions = [
        {
            "question": "你好，我叫王小明，今年28岁，是一名数据科学家，在深圳工作",
            "expected_type": "个人信息提取",
            "description": "测试从自然语言中提取结构化的个人信息"
        },
        {
            "question": "React和Vue.js有什么主要区别？请详细比较一下",
            "expected_type": "技术比较",
            "description": "测试技术对比的结构化输出"
        },
        {
            "question": "我想学习人工智能，能给我一些系统的学习建议吗？",
            "expected_type": "学习建议",
            "description": "测试学习建议的分层次输出"
        }
    ]
    
    for i, demo in enumerate(demo_questions, 1):
        print(f"\n📝 演示 {i}: {demo['expected_type']}")
        print(f"💡 说明: {demo['description']}")
        print(f"❓ 问题: {demo['question']}")
        print("-" * 60)
        
        try:
            # 处理问题
            result = await agent.chat_with_validation(demo['question'])
            
            # 显示结果
            print(f"🎯 问题分类: {result['question_type']}")
            print(f"🔄 处理次数: {result['attempts']}")
            print(f"✅ 验证状态: {'成功' if result['success'] else '失败'}")
            
            print(f"\n🤖 AI回答:")
            print(f"   {result['response'][:200]}{'...' if len(result['response']) > 200 else ''}")
            
            if result['success'] and result['structured_data']:
                print(f"\n📊 结构化数据:")
                formatted_data = json.dumps(
                    result['structured_data'], 
                    ensure_ascii=False, 
                    indent=2
                )
                # 只显示前几行，避免输出过长
                lines = formatted_data.split('\n')
                if len(lines) > 15:
                    print('\n'.join(lines[:15]))
                    print("   ... (数据已截断)")
                else:
                    print(formatted_data)
            
            print("\n" + "=" * 60)
            
        except Exception as e:
            print(f"❌ 演示失败: {str(e)}")
            continue

async def demo_validation_and_retry():
    """演示验证和重试机制"""
    print("\n🔧 验证和重试机制演示")
    print("=" * 60)
    
    agent = EnhancedADKAgent()
    
    # 使用一个可能需要重试的问题
    challenging_question = "比较一下编程语言"
    
    print(f"❓ 挑战性问题: {challenging_question}")
    print("💡 这个问题比较模糊，可能需要多次尝试才能得到结构化输出")
    print("-" * 60)
    
    try:
        result = await agent.chat_with_validation(challenging_question, max_retries=3)
        
        print(f"🎯 最终结果:")
        print(f"   验证状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"   尝试次数: {result['attempts']}")
        print(f"   问题类型: {result['question_type']}")
        
        if result['success']:
            print(f"\n🎉 经过 {result['attempts']} 次尝试，成功获得结构化输出!")
        else:
            print(f"\n⚠️  经过 {result['attempts']} 次尝试，仍未能获得有效的结构化输出")
            print(f"   错误信息: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")

async def demo_different_question_types():
    """演示不同问题类型的分类"""
    print("\n🏷️  问题类型分类演示")
    print("=" * 60)
    
    agent = EnhancedADKAgent()
    
    test_cases = [
        ("我是张三，25岁", "personal_info"),
        ("Python vs Java", "tech_comparison"), 
        ("如何学习机器学习", "learning_advice"),
        ("今天天气如何", "general"),
        ("介绍一下你自己", "personal_info"),
        ("React和Vue的区别", "tech_comparison"),
        ("学编程的建议", "learning_advice"),
        ("什么是人工智能", "general")
    ]
    
    print("测试问题分类准确性:")
    print("-" * 40)
    
    for question, expected_type in test_cases:
        classified_type = agent.classify_question(question)
        status = "✅" if classified_type == expected_type else "❌"
        
        print(f"{status} '{question}' -> {classified_type} (期望: {expected_type})")

def show_supported_models():
    """显示支持的数据模型"""
    print("\n📋 支持的数据模型")
    print("=" * 60)
    
    from enhanced_adk_agent import PersonInfo, TechnicalComparison, LearningAdvice, GeneralResponse
    
    models = [
        ("PersonInfo", PersonInfo, "个人信息提取"),
        ("TechnicalComparison", TechnicalComparison, "技术对比分析"),
        ("LearningAdvice", LearningAdvice, "学习建议生成"),
        ("GeneralResponse", GeneralResponse, "通用问题回答")
    ]
    
    for name, model_class, description in models:
        print(f"\n🏗️  {name} - {description}")
        schema = model_class.model_json_schema()
        
        print("   字段:")
        for field_name, field_info in schema.get('properties', {}).items():
            field_type = field_info.get('type', 'unknown')
            field_desc = field_info.get('description', '无描述')
            required = field_name in schema.get('required', [])
            req_mark = "*" if required else ""
            print(f"     • {field_name}{req_mark}: {field_type} - {field_desc}")

async def main():
    """主演示函数"""
    print("🎭 增强ADK Agent完整演示")
    print("🔗 集成Google ADK + Pydantic验证 + 自动修复")
    print("=" * 80)
    
    try:
        # 显示支持的模型
        show_supported_models()
        
        # 演示问题分类
        await demo_different_question_types()
        
        # 演示基本功能
        await demo_basic_functionality()
        
        # 演示验证和重试
        await demo_validation_and_retry()
        
        print("\n🎉 演示完成!")
        print("💡 提示: 运行 'python test_enhanced_agent.py interactive' 进行交互式测试")
        
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n💥 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
